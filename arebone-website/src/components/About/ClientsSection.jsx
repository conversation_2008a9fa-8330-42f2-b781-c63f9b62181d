import { Building2, MapPin } from 'lucide-react';

const ClientsSection = () => {
  const clients = [
    {
      name: 'Life Healthcare Group',
      logo: '/api/placeholder/150/80',
      category: 'Healthcare'
    },
    {
      name: 'City Power',
      logo: '/api/placeholder/150/80',
      category: 'Utilities'
    },
    {
      name: 'Growthpoint Properties',
      logo: '/api/placeholder/150/80',
      category: 'Property Development'
    },
    {
      name: 'Bidvest Group',
      logo: '/api/placeholder/150/80',
      category: 'Corporate'
    },
    {
      name: 'Belgotex',
      logo: '/api/placeholder/150/80',
      category: 'Manufacturing'
    },
    {
      name: 'BDC',
      logo: '/api/placeholder/150/80',
      category: 'Development Finance'
    },
    {
      name: 'Barzani Group',
      logo: '/api/placeholder/150/80',
      category: 'Property'
    },
    {
      name: 'German Cooperation',
      logo: '/api/placeholder/150/80',
      category: 'International'
    }
  ];

  const locations = [
    'Gauteng',
    'Cape Town',
    'Durban',
    'Polokwane',
    'Kimberley',
    'Bloemfontein',
    'Nelspruit'
  ];

  return (
    <section className="section-padding bg-gray-50">
      <div className="container-custom">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="heading-lg text-secondary-900 mb-4">
            Trusted by Leading Organizations
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            We're proud to serve some of South Africa's most respected companies and organizations, 
            delivering excellence across multiple industries and locations.
          </p>
        </div>

        {/* Clients Grid */}
        <div className="mb-16">
          <h3 className="text-2xl font-semibold text-secondary-900 text-center mb-8">
            Our Valued Clients
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8">
            {clients.map((client, index) => (
              <div
                key={index}
                className="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow duration-300 group"
              >
                <div className="aspect-w-3 aspect-h-2 mb-4">
                  <img
                    src={client.logo}
                    alt={`${client.name} logo`}
                    className="w-full h-16 object-contain filter grayscale group-hover:grayscale-0 transition-all duration-300"
                  />
                </div>
                <div className="text-center">
                  <h4 className="font-medium text-secondary-900 text-sm mb-1">
                    {client.name}
                  </h4>
                  <p className="text-xs text-gray-500">{client.category}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Service Areas */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Locations */}
          <div className="space-y-6">
            <div className="flex items-center space-x-3 mb-6">
              <div className="bg-primary-100 rounded-lg p-3">
                <MapPin className="h-6 w-6 text-primary-600" />
              </div>
              <div>
                <h3 className="text-2xl font-semibold text-secondary-900">
                  Nationwide Coverage
                </h3>
                <p className="text-gray-600">Serving clients across South Africa</p>
              </div>
            </div>
            
            <p className="text-gray-600 leading-relaxed mb-6">
              Our experienced teams operate across seven major cities in South Africa, 
              ensuring we can deliver our services wherever our clients need us most.
            </p>

            <div className="grid grid-cols-2 gap-4">
              {locations.map((location, index) => (
                <div
                  key={index}
                  className="flex items-center space-x-3 p-4 bg-white rounded-lg border border-gray-200"
                >
                  <div className="w-2 h-2 bg-primary-600 rounded-full"></div>
                  <span className="font-medium text-secondary-900">{location}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Certifications & Partnerships */}
          <div className="space-y-6">
            <div className="flex items-center space-x-3 mb-6">
              <div className="bg-primary-100 rounded-lg p-3">
                <Building2 className="h-6 w-6 text-primary-600" />
              </div>
              <div>
                <h3 className="text-2xl font-semibold text-secondary-900">
                  Certifications & Partnerships
                </h3>
                <p className="text-gray-600">Industry-recognized standards</p>
              </div>
            </div>

            <div className="space-y-4">
              <div className="bg-white rounded-xl p-6 border border-gray-200">
                <div className="flex items-center space-x-4">
                  <img
                    src="/api/placeholder/60/60"
                    alt="Belgotex Certification"
                    className="w-12 h-12 object-contain"
                  />
                  <div>
                    <h4 className="font-semibold text-secondary-900">
                      Belgotex Preferred Installer
                    </h4>
                    <p className="text-sm text-gray-600">
                      Certified for professional flooring installation services
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-xl p-6 border border-gray-200">
                <div className="flex items-center space-x-4">
                  <img
                    src="/api/placeholder/60/60"
                    alt="Plascon Certification"
                    className="w-12 h-12 object-contain"
                  />
                  <div>
                    <h4 className="font-semibold text-secondary-900">
                      Plascon Approved Applicator
                    </h4>
                    <p className="text-sm text-gray-600">
                      Authorized for premium paint application services
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-xl p-6 border border-gray-200">
                <div className="flex items-center space-x-4">
                  <img
                    src="/api/placeholder/60/60"
                    alt="Property Point Partnership"
                    className="w-12 h-12 object-contain"
                  />
                  <div>
                    <h4 className="font-semibold text-secondary-900">
                      Property Point Partnership
                    </h4>
                    <p className="text-sm text-gray-600">
                      Two-year incubation program participant
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center mt-16">
          <div className="bg-gradient-to-r from-primary-600 to-primary-700 rounded-2xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-4">
              Join Our Growing List of Satisfied Clients
            </h3>
            <p className="text-primary-100 mb-6 max-w-2xl mx-auto">
              Experience the difference that professional service, quality workmanship, 
              and commitment to excellence can make for your next project.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-white text-primary-600 hover:bg-gray-50 font-medium py-3 px-8 rounded-lg transition-colors duration-200">
                Request Consultation
              </button>
              <button className="border border-white text-white hover:bg-white hover:text-primary-600 font-medium py-3 px-8 rounded-lg transition-colors duration-200">
                View Our Portfolio
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ClientsSection;
