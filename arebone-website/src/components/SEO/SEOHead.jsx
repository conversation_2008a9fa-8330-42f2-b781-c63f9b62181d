import { Helmet } from 'react-helmet-async';

const SEOHead = ({ 
  title = "Arebone Building Enterprise - Professional Construction Services South Africa",
  description = "Arebone Building Enterprise offers professional construction, renovation, maintenance, and specialized building services across South Africa. Belgotex certified installer and Plascon approved applicator.",
  keywords = "construction, building, renovation, maintenance, flooring, painting, South Africa, Belgotex, Plascon, commercial construction, residential construction",
  image = "/og-image.jpg",
  url = "https://arebone.co.za",
  type = "website"
}) => {
  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{title}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords} />
      <meta name="author" content="Arebone Building Enterprise" />
      <meta name="robots" content="index, follow" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <meta charSet="utf-8" />
      <link rel="canonical" href={url} />

      {/* Open Graph Meta Tags */}
      <meta property="og:title" content={title} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={image} />
      <meta property="og:url" content={url} />
      <meta property="og:type" content={type} />
      <meta property="og:site_name" content="Arebone Building Enterprise" />
      <meta property="og:locale" content="en_ZA" />

      {/* Twitter Card Meta Tags */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={title} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={image} />

      {/* Additional Meta Tags */}
      <meta name="theme-color" content="#2563eb" />
      <meta name="msapplication-TileColor" content="#2563eb" />
      
      {/* Business Information */}
      <meta name="geo.region" content="ZA" />
      <meta name="geo.placename" content="South Africa" />
      <meta name="geo.position" content="-26.2041;28.0473" />
      <meta name="ICBM" content="-26.2041, 28.0473" />

      {/* Structured Data */}
      <script type="application/ld+json">
        {JSON.stringify({
          "@context": "https://schema.org",
          "@type": "LocalBusiness",
          "name": "Arebone Building Enterprise",
          "alternateName": "ABE",
          "description": description,
          "url": url,
          "telephone": "+***********",
          "email": "<EMAIL>",
          "address": {
            "@type": "PostalAddress",
            "addressCountry": "ZA",
            "addressRegion": "Gauteng"
          },
          "geo": {
            "@type": "GeoCoordinates",
            "latitude": "-26.2041",
            "longitude": "28.0473"
          },
          "openingHours": "Mo-Fr 07:00-17:00",
          "serviceArea": {
            "@type": "State",
            "name": "South Africa"
          },
          "areaServed": [
            "Gauteng",
            "Cape Town",
            "Durban", 
            "Polokwane",
            "Kimberley",
            "Bloemfontein",
            "Nelspruit"
          ],
          "hasOfferCatalog": {
            "@type": "OfferCatalog",
            "name": "Construction Services",
            "itemListElement": [
              {
                "@type": "Offer",
                "itemOffered": {
                  "@type": "Service",
                  "name": "Construction & Building Work",
                  "description": "Complete construction solutions from foundation to finish"
                }
              },
              {
                "@type": "Offer", 
                "itemOffered": {
                  "@type": "Service",
                  "name": "Renovations & Refurbishment",
                  "description": "Transform your space with comprehensive renovation services"
                }
              },
              {
                "@type": "Offer",
                "itemOffered": {
                  "@type": "Service", 
                  "name": "Maintenance & Repairs",
                  "description": "Professional maintenance services to keep your property in excellent condition"
                }
              },
              {
                "@type": "Offer",
                "itemOffered": {
                  "@type": "Service",
                  "name": "Flooring Solutions", 
                  "description": "Expert flooring installation as a preferred Belgotex installer"
                }
              },
              {
                "@type": "Offer",
                "itemOffered": {
                  "@type": "Service",
                  "name": "Painting & Finishing",
                  "description": "Professional painting services using premium Plascon products"
                }
              }
            ]
          },
          "founder": {
            "@type": "Person",
            "name": "Dwaine Moth",
            "jobTitle": "Founder & CEO"
          },
          "foundingDate": "2012",
          "numberOfEmployees": "10-50",
          "slogan": "Let's See It and Change It",
          "logo": `${url}/logo.png`,
          "image": image,
          "sameAs": [
            url
          ]
        })}
      </script>
    </Helmet>
  );
};

export default SEOHead;
