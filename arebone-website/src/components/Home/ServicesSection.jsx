import { Hammer, PaintBucket, Wrench, Building, Home, Zap } from 'lucide-react';

const ServicesSection = () => {
  const services = [
    {
      icon: Building,
      title: 'Construction',
      description: 'Complete building solutions from foundation to finish, delivering quality construction projects.',
      features: ['New Construction', 'Structural Work', 'Commercial Buildings']
    },
    {
      icon: Home,
      title: 'Renovations',
      description: 'Transform your space with our comprehensive renovation services for residential and commercial properties.',
      features: ['Kitchen Revamps', 'Bathroom Upgrades', 'Space Optimization']
    },
    {
      icon: Wrench,
      title: 'Maintenance',
      description: 'Professional maintenance services to keep your property in excellent condition year-round.',
      features: ['Preventive Maintenance', 'Emergency Repairs', 'Regular Inspections']
    },
    {
      icon: PaintBucket,
      title: 'Painting & Finishing',
      description: 'Expert painting and finishing services using premium Plascon products for lasting results.',
      features: ['Interior Painting', 'Exterior Coating', 'Specialized Finishes']
    },
    {
      icon: Hammer,
      title: 'Flooring Solutions',
      description: 'Professional flooring installation including carpets, laminates, and tiles by Belgotex certified installers.',
      features: ['Carpet Installation', 'Laminate Flooring', 'Tile Work']
    },
    {
      icon: Zap,
      title: 'Specialized Services',
      description: 'Comprehensive range of specialized construction services for unique project requirements.',
      features: ['Dry Walling', 'Plumbing', 'Tenant Installation']
    }
  ];

  return (
    <section className="section-padding bg-white">
      <div className="container-custom">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="heading-lg text-secondary-900 mb-4">
            Our Services
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Comprehensive construction and building services designed to meet all your project needs 
            with professional excellence and quality assurance.
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => {
            const IconComponent = service.icon;
            return (
              <div
                key={index}
                className="group bg-white rounded-xl border border-gray-200 p-8 hover:shadow-xl hover:border-primary-200 transition-all duration-300"
              >
                {/* Icon */}
                <div className="bg-primary-100 rounded-lg p-4 w-16 h-16 flex items-center justify-center mb-6 group-hover:bg-primary-200 transition-colors duration-300">
                  <IconComponent className="h-8 w-8 text-primary-600" />
                </div>

                {/* Content */}
                <div className="space-y-4">
                  <h3 className="text-xl font-semibold text-secondary-900 group-hover:text-primary-600 transition-colors duration-300">
                    {service.title}
                  </h3>
                  
                  <p className="text-gray-600 leading-relaxed">
                    {service.description}
                  </p>

                  {/* Features */}
                  <ul className="space-y-2">
                    {service.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center text-sm text-gray-600">
                        <div className="w-1.5 h-1.5 bg-primary-600 rounded-full mr-3 flex-shrink-0"></div>
                        {feature}
                      </li>
                    ))}
                  </ul>

                  {/* Learn More Link */}
                  <div className="pt-4">
                    <a
                      href="/services"
                      className="text-primary-600 hover:text-primary-700 font-medium text-sm group-hover:underline transition-all duration-300"
                    >
                      Learn More →
                    </a>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* CTA Section */}
        <div className="text-center mt-16">
          <div className="bg-gradient-to-r from-primary-600 to-primary-700 rounded-2xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-4">
              Ready to Start Your Project?
            </h3>
            <p className="text-primary-100 mb-6 max-w-2xl mx-auto">
              Get in touch with our expert team for a free consultation and quote. 
              We're here to bring your vision to life with professional excellence.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-white text-primary-600 hover:bg-gray-50 font-medium py-3 px-8 rounded-lg transition-colors duration-200">
                Get Free Quote
              </button>
              <button className="border border-white text-white hover:bg-white hover:text-primary-600 font-medium py-3 px-8 rounded-lg transition-colors duration-200">
                Call Now: ************
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ServicesSection;
