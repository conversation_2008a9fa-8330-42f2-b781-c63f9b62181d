import { Target, Users, Award, TrendingUp } from 'lucide-react';

const AboutSection = () => {
  const values = [
    {
      icon: Target,
      title: 'Excellence & Quality',
      description: 'Committed to delivering the highest standards in every project we undertake.'
    },
    {
      icon: Users,
      title: 'Youth Development',
      description: 'Empowering young people through skills training and career development opportunities.'
    },
    {
      icon: Award,
      title: 'Professional Standards',
      description: 'Maintaining high work ethics and industry standards as certified professionals.'
    },
    {
      icon: TrendingUp,
      title: 'Growth & Innovation',
      description: 'Continuously evolving our services and embracing new construction technologies.'
    }
  ];

  return (
    <section className="section-padding bg-gray-50">
      <div className="container-custom">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          {/* Content */}
          <div className="space-y-8">
            <div className="space-y-4">
              <h2 className="heading-lg text-slate-900">
                Building a Better Future Through
                <span className="text-orange-600"> Excellence</span>
              </h2>
              <p className="text-lg text-gray-600 leading-relaxed">
                Arebone Building Enterprise (ABE) was established in 2012 by <PERSON><PERSON><PERSON> as a vehicle
                for technical advancement and youth development in South Africa's construction industry.
              </p>
            </div>

            <div className="space-y-6">
              <p className="text-gray-600 leading-relaxed">
                Our mission extends beyond construction – we're committed to addressing the skills shortage
                in South Africa by training and developing young people for successful careers in construction.
                We believe in not just giving someone a 'fish' for a day, but teaching them to 'fish' for a lifetime.
              </p>

              <p className="text-gray-600 leading-relaxed">
                As a preferred Belgotex flooring installer and Plascon Paint approved applicator, we maintain
                the highest industry standards while serving major clients across seven cities in South Africa.
              </p>
            </div>

            {/* Mission Statement */}
            <div className="bg-white rounded-xl p-6 border-l-4 border-orange-600">
              <h3 className="font-semibold text-slate-900 mb-2">Our Mission</h3>
              <p className="text-gray-600 italic">
                "To develop and preserve a good name in the industry and help maintain high work
                and ethical standards for our customers while empowering the next generation."
              </p>
            </div>

            {/* CTA */}
            <div className="flex flex-col sm:flex-row gap-4">
              <button className="btn-primary">
                Learn More About Us
              </button>
              <button className="btn-secondary">
                View Our Projects
              </button>
            </div>
          </div>

          {/* Values Grid */}
          <div className="space-y-8">
            <div className="text-center lg:text-left">
              <h3 className="text-2xl font-semibold text-slate-900 mb-4">
                Our Core Values
              </h3>
              <p className="text-gray-600">
                The principles that guide everything we do and every project we deliver.
              </p>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
              {values.map((value, index) => {
                const IconComponent = value.icon;
                return (
                  <div
                    key={index}
                    className="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow duration-300"
                  >
                    <div className="bg-orange-100 rounded-lg p-3 w-12 h-12 flex items-center justify-center mb-4">
                      <IconComponent className="h-6 w-6 text-orange-600" />
                    </div>
                    <h4 className="font-semibold text-slate-900 mb-2">
                      {value.title}
                    </h4>
                    <p className="text-sm text-gray-600 leading-relaxed">
                      {value.description}
                    </p>
                  </div>
                );
              })}
            </div>

            {/* Achievement Highlight */}
            <div className="bg-gradient-to-r from-orange-600 to-orange-700 rounded-xl p-6 text-white">
              <div className="flex items-center space-x-4">
                <div className="bg-white/20 rounded-lg p-3">
                  <Award className="h-8 w-8 text-white" />
                </div>
                <div>
                  <h4 className="font-semibold text-lg">Property Point Partnership</h4>
                  <p className="text-orange-100 text-sm">
                    Selected for a two-year incubation program with Property Point,
                    a Growthpoint initiative.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutSection;
