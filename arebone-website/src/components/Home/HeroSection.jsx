import { ArrowRight } from 'lucide-react';

const HeroSection = () => {
  return (
    <section className="relative h-[85vh] min-h-[700px] max-h-[900px] flex items-center justify-center overflow-hidden">
      {/* Full-Screen Background Image */}
      <div className="absolute inset-0 z-0">
        <picture>
          <source
            media="(min-width: 1024px)"
            srcSet="/images/construction-hero-hd.webp"
            type="image/webp"
          />
          <source
            media="(min-width: 1024px)"
            srcSet="/images/construction-hero-hd.jpg"
          />
          <source
            media="(min-width: 768px)"
            srcSet="/images/construction-hero-hd.webp"
            type="image/webp"
          />
          <source
            media="(min-width: 768px)"
            srcSet="/images/construction-hero-hd.jpg"
          />
          <img
            src="/images/construction-hero-hd.jpg"
            alt="Professional construction team working on building project - Arebone Building Enterprise"
            className="hero-background-image"
            loading="eager"
          />
        </picture>

        {/* Modern Clean Overlay with Construction Theme */}
        <div className="absolute inset-0 bg-gradient-to-b from-slate-900/70 via-slate-900/50 to-slate-900/80"></div>
        <div className="absolute inset-0 bg-gradient-to-r from-orange-900/15 via-transparent to-blue-900/15"></div>
        <div className="absolute inset-0 bg-gradient-to-t from-slate-900/85 via-transparent to-transparent"></div>

        {/* Construction-themed geometric elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-1/4 left-1/4 w-3 h-3 bg-orange-400/20 rounded-sm rotate-45 animate-float-slow"></div>
          <div className="absolute top-3/4 right-1/3 w-2 h-2 bg-blue-400/25 rounded-sm animate-float-medium"></div>
          <div className="absolute top-1/2 right-1/4 w-2.5 h-2.5 bg-orange-300/15 rounded-sm rotate-12 animate-float-fast"></div>
          <div className="absolute top-1/3 right-1/2 w-1 h-8 bg-orange-400/10 animate-float-slow" style={{ animationDelay: '2s' }}></div>
          <div className="absolute bottom-1/3 left-1/3 w-6 h-1 bg-blue-400/15 animate-float-medium" style={{ animationDelay: '1s' }}></div>
        </div>
      </div>

      {/* Centered Content Overlay */}
      <div className="container-custom relative z-10 pt-16 pb-8 lg:pt-20 lg:pb-12 text-center">
        <div className="max-w-5xl mx-auto space-y-8">
          {/* Headline Section - Compact Modern Typography */}
          <div className="space-y-6 animate-fade-in-up">
            {/* Decorative Element */}
            <div className="flex justify-center mb-4">
              <div className="w-16 h-0.5 bg-gradient-to-r from-transparent via-blue-400 to-transparent opacity-60"></div>
            </div>

            <h1 className="text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-black tracking-[-0.02em] leading-[0.9] text-white hero-text-shadow selection:bg-orange-100 relative">
              <span className="block text-orange-400 mb-1 lg:mb-2 animate-slide-in-left" style={{ animationDelay: '0.2s' }}>
                BUILD
              </span>
              <span className="block text-white mb-1 lg:mb-2 animate-slide-in-right" style={{ animationDelay: '0.4s' }}>
                THE FUTURE
              </span>
              <span className="block text-blue-400 text-3xl md:text-4xl lg:text-5xl xl:text-6xl animate-slide-in-left" style={{ animationDelay: '0.6s' }}>
                WITH PRECISION
              </span>
              {/* Subtle glow effect */}
              <div className="absolute inset-0 text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-black tracking-[-0.02em] leading-[0.9] opacity-15 blur-sm pointer-events-none">
                <span className="block text-orange-400 mb-1 lg:mb-2">BUILD</span>
                <span className="block text-white mb-1 lg:mb-2">THE FUTURE</span>
                <span className="block text-blue-400 text-3xl md:text-4xl lg:text-5xl xl:text-6xl">WITH PRECISION</span>
              </div>
            </h1>

            {/* Enhanced Subheading */}
            <div className="relative animate-fade-in" style={{ animationDelay: '0.8s' }}>
              <h2 className="text-base md:text-lg lg:text-xl font-medium text-white/95 max-w-4xl mx-auto leading-[1.4] tracking-[0.01em] drop-shadow-lg">
                Professional civil engineering and construction services across South Africa
              </h2>
              <p className="text-sm md:text-base text-white/80 max-w-3xl mx-auto mt-3 leading-relaxed">
                From residential renovations to commercial projects – we deliver excellence with 12+ years of proven expertise
              </p>
              {/* Modern accent line with gradient */}
              <div className="w-32 h-px bg-gradient-to-r from-orange-400/60 via-blue-400/60 to-transparent mx-auto mt-4"></div>
            </div>

          </div>

          {/* Enhanced CTA Buttons with Modern Orange/Blue Theme */}
          <div className="space-y-6 animate-fade-in-up" style={{ animationDelay: '1s' }}>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              {/* Primary CTA with orange gradient */}
              <button className="relative bg-gradient-to-r from-orange-600 to-orange-700 hover:from-orange-700 hover:to-orange-800 text-white font-semibold px-8 py-4 rounded-2xl transition-all duration-700 ease-out hover:shadow-2xl hover:shadow-orange-500/40 hover:-translate-y-2 group text-lg tracking-wide overflow-hidden">
                {/* Button shine effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out"></div>
                <span className="relative z-10">Get Free Quote</span>
                <ArrowRight className="relative z-10 ml-3 h-4 w-4 group-hover:translate-x-3 transition-transform duration-700 ease-out" />
              </button>

              {/* Secondary CTA with blue accent */}
              <button className="relative bg-white/5 hover:bg-white/15 text-white font-semibold px-8 py-4 rounded-2xl border border-blue-400/30 hover:border-blue-400/50 transition-all duration-700 ease-out hover:shadow-xl hover:shadow-blue-400/20 hover:-translate-y-2 text-lg tracking-wide backdrop-blur-xl group overflow-hidden">
                {/* Subtle glow on hover */}
                <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-orange-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>
                <span className="relative z-10">View Our Work</span>
              </button>
            </div>


          </div>

          {/* Compact Stats with Modern Design */}
          <div className="pt-6 relative animate-fade-in-up" style={{ animationDelay: '1s' }}>
            {/* Decorative border with gradient */}
            <div className="w-full h-px bg-gradient-to-r from-transparent via-white/30 to-transparent mb-6"></div>

            <div className="grid grid-cols-3 gap-4 md:gap-8 max-w-4xl mx-auto">
              {[
                { number: '12+', label: 'Years\nExperience', delay: '1.2s', color: 'orange' },
                { number: '500+', label: 'Projects\nCompleted', delay: '1.4s', color: 'blue' },
                { number: '7', label: 'Cities\nServed', delay: '1.6s', color: 'orange' }
              ].map((stat, index) => {
                const isOrange = stat.color === 'orange';

                return (
                  <div key={index} className="text-center group cursor-default animate-fade-in-up" style={{ animationDelay: stat.delay }}>
                    {/* Background glow effect */}
                    <div className="relative">
                      <div className={`absolute inset-0 rounded-full blur-2xl scale-150 opacity-0 group-hover:opacity-100 transition-opacity duration-700 ${isOrange ? 'bg-orange-500/10' : 'bg-blue-500/10'}`}></div>

                      {/* Number with enhanced styling */}
                      <div className={`relative text-3xl md:text-4xl lg:text-5xl font-black text-white mb-3 transition-all duration-700 ease-out tracking-[-0.02em] drop-shadow-lg transform group-hover:scale-110 ${isOrange ? 'group-hover:text-orange-400' : 'group-hover:text-blue-400'}`}>
                        {stat.number}
                        {/* Subtle number glow */}
                        <div className={`absolute inset-0 text-3xl md:text-4xl lg:text-5xl font-black blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-700 ${isOrange ? 'text-orange-400/20' : 'text-blue-400/20'}`}>
                          {stat.number}
                        </div>
                      </div>

                      {/* Enhanced label */}
                      <div className="text-xs md:text-sm text-white/80 font-medium tracking-[0.1em] uppercase leading-relaxed drop-shadow-md whitespace-pre-line group-hover:text-white transition-colors duration-700">
                        {stat.label}
                      </div>

                      {/* Subtle accent line */}
                      <div className={`w-6 h-0.5 bg-gradient-to-r to-transparent mx-auto mt-2 opacity-0 group-hover:opacity-100 transition-opacity duration-700 ${isOrange ? 'from-orange-400/60' : 'from-blue-400/60'}`}></div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>


        </div>
      </div>


    </section>
  );
};

export default HeroSection;
