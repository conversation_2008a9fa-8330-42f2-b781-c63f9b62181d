import { CheckCircle, ArrowRight } from 'lucide-react';

const ServiceDetails = () => {
  const services = [
    {
      id: 'construction',
      title: 'Construction & Building Work',
      description: 'Complete construction solutions from foundation to finish, delivering quality projects on time and within budget.',
      image: '/api/placeholder/600/400',
      features: [
        'New construction projects',
        'Structural modifications',
        'Commercial building work',
        'Residential construction',
        'Foundation work',
        'Roofing solutions'
      ],
      process: [
        'Site assessment and planning',
        'Detailed project proposal',
        'Construction execution',
        'Quality control and finishing'
      ]
    },
    {
      id: 'renovations',
      title: 'Renovations & Refurbishment',
      description: 'Transform your space with our comprehensive renovation services, breathing new life into existing structures.',
      image: '/api/placeholder/600/400',
      features: [
        'Kitchen and bathroom revamps',
        'Office space renovations',
        'Retail space upgrades',
        'Interior modifications',
        'Space optimization',
        'Modern design integration'
      ],
      process: [
        'Space evaluation and design',
        'Renovation planning',
        'Phased implementation',
        'Final inspection and handover'
      ]
    },
    {
      id: 'maintenance',
      title: 'Maintenance & Repairs',
      description: 'Professional maintenance services to keep your property in excellent condition with preventive and corrective solutions.',
      image: '/api/placeholder/600/400',
      features: [
        'Preventive maintenance programs',
        'Emergency repair services',
        'Building inspections',
        'Facility management',
        'Scheduled maintenance',
        '24/7 emergency response'
      ],
      process: [
        'Property assessment',
        'Maintenance schedule creation',
        'Regular service delivery',
        'Performance monitoring'
      ]
    },
    {
      id: 'flooring',
      title: 'Flooring Solutions',
      description: 'Expert flooring installation and maintenance services as a preferred Belgotex installer, ensuring quality and durability.',
      image: '/api/placeholder/600/400',
      features: [
        'Carpet installation',
        'Laminate flooring',
        'Vinyl and LVT installation',
        'Tile work',
        'Floor preparation',
        'Flooring maintenance'
      ],
      process: [
        'Floor assessment and measurement',
        'Material selection and sourcing',
        'Professional installation',
        'Quality assurance and cleanup'
      ]
    },
    {
      id: 'painting',
      title: 'Painting & Finishing',
      description: 'Professional painting services using premium Plascon products, delivering exceptional finishes for interior and exterior applications.',
      image: '/api/placeholder/600/400',
      features: [
        'Interior painting',
        'Exterior coating',
        'Specialized finishes',
        'Surface preparation',
        'Color consultation',
        'Protective coatings'
      ],
      process: [
        'Surface preparation and priming',
        'Color selection and testing',
        'Professional application',
        'Final inspection and touch-ups'
      ]
    },
    {
      id: 'specialized',
      title: 'Specialized Services',
      description: 'Comprehensive range of specialized construction services including plumbing, electrical, and custom installations.',
      image: '/api/placeholder/600/400',
      features: [
        'Dry walling and partitioning',
        'Ceiling installations',
        'Plumbing services',
        'Tenant installations',
        'Shop front installations',
        'Demolition services'
      ],
      process: [
        'Technical assessment',
        'Specialized planning',
        'Expert execution',
        'Compliance verification'
      ]
    }
  ];

  return (
    <section className="section-padding bg-white">
      <div className="container-custom">
        <div className="space-y-20">
          {services.map((service, index) => (
            <div
              key={service.id}
              className={`grid grid-cols-1 lg:grid-cols-2 gap-12 items-center ${
                index % 2 === 1 ? 'lg:grid-flow-col-dense' : ''
              }`}
            >
              {/* Content */}
              <div className={`space-y-6 ${index % 2 === 1 ? 'lg:col-start-2' : ''}`}>
                <div className="space-y-4">
                  <h3 className="heading-md text-secondary-900">
                    {service.title}
                  </h3>
                  <p className="text-lg text-gray-600 leading-relaxed">
                    {service.description}
                  </p>
                </div>

                {/* Features */}
                <div className="space-y-4">
                  <h4 className="text-lg font-semibold text-secondary-900">
                    What We Offer:
                  </h4>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                    {service.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-center space-x-3">
                        <CheckCircle className="h-5 w-5 text-primary-600 flex-shrink-0" />
                        <span className="text-gray-700">{feature}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Process */}
                <div className="space-y-4">
                  <h4 className="text-lg font-semibold text-secondary-900">
                    Our Process:
                  </h4>
                  <div className="space-y-2">
                    {service.process.map((step, stepIndex) => (
                      <div key={stepIndex} className="flex items-center space-x-3">
                        <div className="bg-primary-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-medium">
                          {stepIndex + 1}
                        </div>
                        <span className="text-gray-700">{step}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* CTA */}
                <div className="pt-4">
                  <button className="btn-primary group">
                    Get Quote for {service.title}
                    <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-200" />
                  </button>
                </div>
              </div>

              {/* Image */}
              <div className={`${index % 2 === 1 ? 'lg:col-start-1' : ''}`}>
                <div className="relative">
                  <img
                    src={service.image}
                    alt={service.title}
                    className="rounded-2xl shadow-lg w-full h-80 object-cover"
                  />
                  {/* Overlay Badge */}
                  <div className="absolute top-6 left-6 bg-white rounded-lg px-4 py-2 shadow-md">
                    <span className="text-primary-600 font-semibold text-sm">
                      Professional Service
                    </span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ServiceDetails;
